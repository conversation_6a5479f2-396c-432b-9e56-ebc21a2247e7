import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "./stores/**/*.{js,ts,jsx,tsx,mdx}",
    "./lib/**/*.{js,ts,jsx,tsx,mdx}",
  ],
  darkMode: "class",
  theme: {
  	extend: {
  		fontFamily: {
  			sans: [
  				'Inter',
  				'system-ui',
  				'sans-serif'
  			]
  		},
  		colors: {
  			primary: {
  				'100': '#EBE1C3',
  				'200': '#E1D3A7',
  				'300': '#D3BF7E',
  				'400': '#CAB265',
  				'500': '#BD9F3F',
  				'600': '#AC9139',
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				'50': '#EBEBEB',
  				'100': '#C0C0C0',
  				'300': '#767676',
  				'400': '#5C5C5C',
  				'500': '#333333',
  				'600': '#2E2E2E',
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			white: {
  				'50': '#FDFEFE',
  				DEFAULT: '#FFFFFF'
  			},
  			button: {
  				'background-primary': '#BD9F3F',
  				'background-primary-disable': '#EBE1C3',
  				'background-secondary': '#333333',
  				'background-secondary-disable': '#2E2E2E',
  				'background-tertiary': '#2E2E2E',
  				'background-tertiary-disable': '#242424',
  				'background-quaternary': '#242424',
  				'background-quaternary-disable': '#1C1C1C',
  				'background-quinary': '#1C1C1C',
  				'background-quinary-disable': '#141414',
  				'background-senary': '#141414',
  				'background-senary-disable': '#0A0A0A',
  				'background-septenary': '#0A0A0A',
  				'background-septenary-disable': '#000000',
  				'background-icon': '#FDFEFE29'
  			},
  			gray: {
  				'600': '#A0A0A0',
  				'950': '#1C1C1C'
  			},
  			custom: {
  				translucentDark: '#2424243D',
  				backgroundLight: '#F2F2F2',
  				borderLight: '#EFEFEF'
  			},
  			dark: {
  				primary: {
  					'100': '#EBE1C3',
  					'200': '#E1D3A7',
  					'300': '#D3BF7E',
  					'400': '#CAB265',
  					'500': '#BD9F3F',
  					'600': '#AC9139'
  				},
  				secondary: {
  					'50': '#EBEBEB',
  					'100': '#C0C0C0',
  					'300': '#767676',
  					'400': '#5C5C5C',
  					'500': '#333333',
  					'600': '#2E2E2E'
  				},
  				white: {
  					'50': '#FDFEFE',
  					DEFAULT: '#FFFFFF'
  				},
  				button: {
  					'background-primary': '#BD9F3F',
  					'background-primary-disable': '#EBE1C3',
  					'background-secondary': '#333333',
  					'background-secondary-disable': '#2E2E2E',
  					'background-tertiary': '#2E2E2E',
  					'background-tertiary-disable': '#242424',
  					'background-quaternary': '#242424',
  					'background-quaternary-disable': '#1C1C1C',
  					'background-quinary': '#1C1C1C',
  					'background-quinary-disable': '#141414',
  					'background-senary': '#141414',
  					'background-senary-disable': '#0A0A0A',
  					'background-septenary': '#0A0A0A',
  					'background-septenary-disable': '#000000',
  					'background-icon': '#FDFEFE29'
  				},
  				gray: {
  					'600': '#A0A0A0',
  					'950': '#1C1C1C'
  				},
  				custom: {
  					translucentDark: '#2424243D',
  					backgroundLight: '#F2F2F2',
  					borderLight: '#EFEFEF'
  				}
  			},
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		}
  	}
  },
  plugins: [
    require('tailwindcss-rtl'),
      require("tailwindcss-animate")
],
};

export default config;