"use client"

import { useEffect, useRef } from "react"
import { useSidebarStore } from "@/stores/sidebarStore"
import { useTranslation } from "react-i18next"

interface MenuItemProps {
  label: string
  href?: string
  onClick?: () => void
  showArrow?: boolean
}

function MenuItem({ label, href = "#", onClick, showArrow = true }: MenuItemProps) {
  const handleClick = () => {
    if (onClick) {
      onClick()
    }
  }

  return (
    <a
      href={href}
      onClick={handleClick}
      className="flex items-center justify-between px-6 py-4
        text-gray-700 dark:text-slate-300
        hover:bg-gray-100 dark:hover:bg-slate-700
        transition-colors duration-200
        border-b border-gray-100 dark:border-slate-700"
    >
      <span className="text-base font-medium">{label}</span>
      {showArrow && (
        <svg
          className="w-5 h-5 text-gray-400 dark:text-slate-500"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M9 5l7 7-7 7"
          />
        </svg>
      )}
    </a>
  )
}

export function SideMenuBar() {
  const { isOpen, closeSidebar } = useSidebarStore()
  const { t } = useTranslation()
  const sidebarRef = useRef<HTMLDivElement>(null)

  // Close sidebar when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        sidebarRef.current &&
        !sidebarRef.current.contains(event.target as Node)
      ) {
        closeSidebar()
      }
    }

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside)
      // Prevent body scroll when sidebar is open
      document.body.style.overflow = "hidden"
    } else {
      document.body.style.overflow = "unset"
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
      document.body.style.overflow = "unset"
    }
  }, [isOpen, closeSidebar])

  // Close sidebar on escape key
  useEffect(() => {
    function handleEscape(event: KeyboardEvent) {
      if (event.key === "Escape") {
        closeSidebar()
      }
    }

    if (isOpen) {
      document.addEventListener("keydown", handleEscape)
    }

    return () => {
      document.removeEventListener("keydown", handleEscape)
    }
  }, [isOpen, closeSidebar])

  if (!isOpen) return null

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-999 transition-opacity duration-300"
        onClick={closeSidebar}
      />

      {/* Sidebar */}
      <div
        ref={sidebarRef}
        className={`fixed top-0 left-0 h-full w-80 z-50
          bg-white-50 dark:bg-dark-secondary-600
          shadow-xl transform transition-transform duration-300 ease-in-out
          ${isOpen ? "translate-x-0" : "-translate-x-full"}
          flex flex-col`}
      >
        {/* Header with close button */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-slate-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-slate-100">
            Menu
          </h2>
          <button
            type="button"
            onClick={closeSidebar}
            className="p-2 rounded-lg text-gray-600 dark:text-slate-400
              hover:text-gray-900 dark:hover:text-slate-100
              hover:bg-gray-100 dark:hover:bg-slate-700
              transition-colors duration-200"
            aria-label="Close sidebar"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Navigation Menu Items */}
        <div className="flex-1 overflow-y-auto">
          <nav className="py-2">
            <MenuItem label={t("navigation.home")} />
            <MenuItem label={t("navigation.about")} />
            <MenuItem label={t("navigation.services")} />
            <MenuItem label="Market Insights" />
            <MenuItem label="Blog" />
            <MenuItem label="Contact" />

            {/* Separator line */}
            <div className="my-4 border-t border-gray-200 dark:border-slate-700" />

            {/* Footer menu items */}
            <MenuItem label="Terms of use" />
            <MenuItem label="Privacy Policy" showArrow={false} />
            <MenuItem label="Security Policy" showArrow={false} />
            <MenuItem label="Cookie Settings" showArrow={false} />
          </nav>
        </div>

        {/* Investment Button */}
        <div className="p-6 border-t border-gray-200 dark:border-slate-700">
          <button
            type="button"
            className="w-full py-3 px-4
              bg-primary-500 hover:bg-primary-600
              text-white font-semibold rounded-lg
              transition-colors duration-200
              focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2"
            onClick={closeSidebar}
          >
            Investment
          </button>
        </div>
      </div>
    </>
  )
}