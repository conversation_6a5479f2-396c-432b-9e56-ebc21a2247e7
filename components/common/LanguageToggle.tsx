"use client"

import { useLanguageStore, type Language } from "@/stores/languageStore"
import { useTranslation } from "react-i18next"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Globe, Check } from "lucide-react"

export function LanguageToggle() {
  const { language, setLanguage } = useLanguageStore()
  const { t } = useTranslation()

  const languages = [
    { code: "en" as Language, label: "English", flag: "🇺🇸" },
    { code: "ar" as Language, label: "العربية", flag: "🇸🇦" }
  ]

  const currentLanguage = languages.find((lang) => lang.code === language)

  const handleLanguageSelect = (langCode: Language) => {
    setLanguage(langCode)
  }

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          size="sm"
          className="flex items-center gap-2 px-3 h-10 rounded-lg
            hover:bg-gray-100 dark:hover:bg-gray-700
            transition-colors duration-200
            text-sm font-medium
            text-gray-700 dark:text-gray-300"
          title={t("language.toggle_language")}
          aria-label={t("language.toggle_language")}
        >
          <Globe className="w-5 h-5" />
          <span className="hidden sm:inline">
            {currentLanguage?.code.toUpperCase()}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-40">
        {languages.map((lang) => (
          <DropdownMenuItem
            key={lang.code}
            onClick={() => handleLanguageSelect(lang.code)}
            className="flex items-center gap-3 cursor-pointer"
          >
            <span className="text-lg">{lang.flag}</span>
            <span className="flex-1">{lang.label}</span>
            {language === lang.code && (
              <Check className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
