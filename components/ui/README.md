# shadcn/ui Components

This folder contains all the shadcn/ui components for the project. These components are built on top of Radix UI primitives and styled with Tailwind CSS.

## 🚀 Setup Complete

The shadcn/ui library has been successfully installed and configured with the following setup:

### ✅ **Installed Dependencies:**
- `class-variance-authority` - For component variants
- `clsx` - For conditional class names
- `tailwind-merge` - For merging Tailwind classes
- `lucide-react` - For icons
- `tailwindcss-animate` - For animations

### ✅ **Configuration Files:**
- `components.json` - shadcn/ui configuration
- `lib/utils.ts` - Utility functions (cn helper)
- Updated `tailwind.config.ts` with shadcn/ui theme variables
- Updated `app/globals.css` with CSS variables

### ✅ **Available Components:**
- `Button` - Various button variants and sizes
- `Card` - Card components with header, content, etc.
- `Input` - Form input fields
- `Label` - Form labels

## 📖 Usage

Import components from the `@/components/ui` directory:

```tsx
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

function MyComponent() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Example Card</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input id="email" type="email" placeholder="Enter email" />
          <Button>Submit</Button>
        </div>
      </CardContent>
    </Card>
  )
}
```

## 🎨 Theming

The components automatically adapt to your existing theme system:
- Light/Dark mode support via CSS variables
- Integrates with your existing Tailwind color palette
- Maintains consistency with your design system

## 📦 Adding More Components

To add more shadcn/ui components:

```bash
npx shadcn@latest add [component-name]
```

For example:
```bash
npx shadcn@latest add dialog
npx shadcn@latest add dropdown-menu
npx shadcn@latest add toast
```

## 🔧 Customization

Components can be customized by:
1. Modifying the component files directly in `components/ui/`
2. Updating CSS variables in `app/globals.css`
3. Extending the theme in `tailwind.config.ts`

## 📚 Documentation

For full documentation and component examples, visit:
- [shadcn/ui Documentation](https://ui.shadcn.com/)
- [Component Examples](https://ui.shadcn.com/docs/components)

## 🎯 Demo

Check out the `ShadcnDemo` component in `components/demo/ShadcnDemo.tsx` to see the components in action.
