@tailwind base;
@tailwind components;
@tailwind utilities;

/* Base reset and styles */
* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

html {
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  text-size-adjust: 100%;
  -moz-tab-size: 4;
  tab-size: 4;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif;
  font-feature-settings: normal;
  font-variation-settings: normal;
}

body {
  margin: 0;
  line-height: inherit;
  max-width: 100vw;
  overflow-x: hidden;
  @apply bg-white dark:bg-slate-900 text-gray-900 dark:text-slate-100;
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Remove default button styles */
button {
  font-family: inherit;
  font-size: 100%;
  font-weight: inherit;
  line-height: inherit;
  color: inherit;
  margin: 0;
  padding: 0;
  background-color: transparent;
  background-image: none;
  border: 0;
  cursor: pointer;
}

/* Remove default link styles */
a {
  color: inherit;
  text-decoration: inherit;
}

/* Custom scrollbar for dark mode */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-slate-800;
}

::-webkit-scrollbar-thumb {
  @apply bg-gray-300 dark:bg-slate-600 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-400 dark:bg-gray-500;
}

/* Focus styles */
:focus-visible {
  @apply outline-2 outline-blue-500 dark:outline-blue-400 outline-offset-2;
}
